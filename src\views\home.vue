<template>
  <el-row :gutter="20">
    <!-- 数据概览模块 -->
    <el-col :span="24">
      <div class="grid-content ep-bg-purple">
        <span class="title">数据概览</span>
        <el-row :gutter="24" style="margin-top: 15px;">
          <el-col :span="6">
            <div class="data-card data-card-all">
              <div class="data-overview">
                <div class="semi-circle-progress">
                  <svg width="100" height="60" viewBox="0 0 100 60">
                    <!-- 背景半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#e5e7eb"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"/>
                    <!-- 进度半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#667eea"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"
                          :stroke-dasharray="`${125.66 * 100 / 100} 125.66`"
                          stroke-dashoffset="0"/>
                  </svg>
                  <div class="semi-circle-content">
                    <div class="icon-container">
                      <el-icon color="#667eea" size="20px"><Tickets /></el-icon>
                    </div>
                    <div class="percentage-text">100%</div>
                  </div>
                </div>
              </div>
              <div class="message">
                <span class="message-label">全部信息</span>
                <h3 class="message-value">{{ allCount }}</h3>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="data-card data-card-negative">
              <div class="data-overview">
                <div class="semi-circle-progress">
                  <svg width="100" height="60" viewBox="0 0 100 60">
                    <!-- 背景半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#e5e7eb"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"/>
                    <!-- 进度半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#ef4444"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"
                          :stroke-dasharray="`${125.66 * percentage1 / 100} 125.66`"
                          stroke-dashoffset="0"/>
                  </svg>
                  <div class="semi-circle-content">
                    <div class="icon-container">
                      <el-icon color="#ef4444" size="20px"><CloseBold /></el-icon>
                    </div>
                    <div class="percentage-text">{{ percentage1 }}%</div>
                  </div>
                </div>
              </div>
              <div class="message">
                <span class="message-label">负面信息</span>
                <h3 class="message-value">{{ badCount }}</h3>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="data-card data-card-positive">
              <div class="data-overview">
                <div class="semi-circle-progress">
                  <svg width="100" height="60" viewBox="0 0 100 60">
                    <!-- 背景半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#e5e7eb"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"/>
                    <!-- 进度半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#10b981"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"
                          :stroke-dasharray="`${125.66 * percentage2 / 100} 125.66`"
                          stroke-dashoffset="0"/>
                  </svg>
                  <div class="semi-circle-content">
                    <div class="icon-container">
                      <el-icon color="#10b981" size="20px"><Tickets /></el-icon>
                    </div>
                    <div class="percentage-text">{{ percentage2 }}%</div>
                  </div>
                </div>
              </div>
              <div class="message">
                <span class="message-label">正面信息</span>
                <h3 class="message-value">{{ goodCount }}</h3>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="data-card data-card-neutral">
              <div class="data-overview">
                <div class="semi-circle-progress">
                  <svg width="100" height="60" viewBox="0 0 100 60">
                    <!-- 背景半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#e5e7eb"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"/>
                    <!-- 进度半圆 -->
                    <path d="M 10 50 A 40 40 0 0 1 90 50"
                          stroke="#f59e0b"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round"
                          :stroke-dasharray="`${125.66 * percentage3 / 100} 125.66`"
                          stroke-dashoffset="0"/>
                  </svg>
                  <div class="semi-circle-content">
                    <div class="icon-container">
                      <el-icon color="#f59e0b" size="20px"><Bell /></el-icon>
                    </div>
                    <div class="percentage-text">{{ percentage3 }}%</div>
                  </div>
                </div>
              </div>
              <div class="message">
                <span class="message-label">中性信息</span>
                <h3 class="message-value">{{ neutralCount }}</h3>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-col>
  </el-row>

  <el-row :gutter="20" style="margin-top: 20px;">
    <!-- 最新资讯模块 -->
    <el-col :span="12">
      <div class="grid-content ep-bg-purple hight">
        <span class="title">最新资讯</span>
        <div 
          v-for="item in filterDetailList" 
          :key="item.id" 
          class="detail-item"
        >
          <div class="detail-title">
            <div 
              class="detail-title-left" 
              @click="to(item.url)"
            >
              {{ item.name.substring(0, 40) }}
            </div>
            <span 
              class="detail-title-right" 
              :style="{ color: getColor(item.natureId) }"
            >
              <span 
                style="background-color: rgba(255, 255, 255, 0.562); padding: 2px; font-size: 12px"
              >
                {{ getNature(item.natureId) }}
              </span>
            </span>
          </div>
          <div class="detail-footer">
            {{ item.time }} {{ item.source }}
          </div>
        </div>
      </div>
    </el-col>

    <!-- 情感占比模块 -->
    <el-col :span="12">
      <div class="grid-content ep-bg-purple hight">
        <span class="title">情感占比</span> 
        <div id="emotional_ratio" class="emotional_ratio"></div>
        <div class="emotional-ratio-info">
          <div class="ratio-item">
            <div class="ratio-value" style="color: #10b981">{{ percentage2 }}%</div>
            <div class="ratio-label">正面</div>
          </div>
          <div class="ratio-item">
            <div class="ratio-value" style="color: #f59e0b">{{ percentage3 }}%</div>
            <div class="ratio-label">中性</div>
          </div>
          <div class="ratio-item">
            <div class="ratio-value" style="color: #ef4444">{{ percentage1 }}%</div>
            <div class="ratio-label">负面</div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
import router from "@/router";
import baseService from "@/service/baseService";
import * as echarts from "echarts";
import { onMounted, ref } from "vue";
// 引入 Element Plus 图标（根据实际引入方式调整，这里假设是按需引入）
import { Tickets, CloseBold, Bell } from "@element-plus/icons-vue"; 

const detail = ref("");
//总数量
const allCount = ref(0);
//负面数量
const badCount = ref(0);
//正面数量
const goodCount = ref(0);
//中性数量
const neutralCount = ref(0);
const percentage = ref(0);
const percentage1 = ref(0);
const percentage2 = ref(0);
const percentage3 = ref(0);
const natureList = ref([]);
const filterDetailList = ref([]);

const getColor = (natureId) => {
  const color = {
    1: "orange",
    2: "red",
    3: "green",
    4: "blue"
  };
  return color[natureId];
};

const getNature = (natureId) => {
  return natureList.value.find((item) => item.id == natureId)?.name || "";
};

baseService.get("vsafety/nature/page").then((res) => {
  natureList.value = res.data.list;
});

//跳转到详情页
const to = (url) => {
  router.push({ path: "/vsafety/envdetail", state: { url } });
};

baseService.get("es", { size: 1 }).then((res) => {
  detail.value = res.data.content;
  filterDetailList.value = detail.value.slice(0, 4).sort((a, b) => new Date(b.time) - new Date(a.time));
  allCount.value = res.data.numberOfElements;
  badCount.value = res.data.content.filter((item) => item.natureId == 2).length;
  goodCount.value = res.data.content.filter((item) => item.natureId == 3).length;
  neutralCount.value = res.data.content.filter((item) => item.natureId == 1).length;

  percentage1.value = Number(((badCount.value / allCount.value) * 100).toFixed(2));
  percentage2.value = Number(((goodCount.value / allCount.value) * 100).toFixed(2));
  percentage3.value = Number(((neutralCount.value / allCount.value) * 100).toFixed(2));

  const myChart = echarts.init(document.getElementById("emotional_ratio"));
  myChart.setOption({
    tooltip: {
      trigger: "item"
    },
    series: [
      {
        name: "情感占比",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: goodCount.value, name: "正面", itemStyle: { color: '#10b981' } },
          { value: neutralCount.value, name: "中性", itemStyle: { color: '#f59e0b' } },
          { value: badCount.value, name: "负面", itemStyle: { color: '#ef4444' } }
        ]
      }
    ]
  });
});

onMounted(() => {
  // 可在这里做一些初始化操作，比如监听窗口变化重新渲染图表等
  window.addEventListener('resize', () => {
    const myChart = echarts.getInstanceByDom(document.getElementById("emotional_ratio"));
    myChart && myChart.resize();
  });
});
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 5px;
}

.grid-content {
  border-radius: 16px;
  min-height: 160px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.grid-content:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.title {
  font-size: 20px;
  font-weight: 700;
  display: block;
  margin-bottom: 16px;
  color: #1e293b;
  position: relative;
  padding-left: 12px;
}

.title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* 数据概览卡片样式 */
.data-card {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 不同类型卡片的特定样式 */
.data-card-all::before {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.data-card-negative::before {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.data-card-positive::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.data-card-neutral::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

/* 半圆环进度条样式 */
.semi-circle-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.semi-circle-progress svg {
  margin-bottom: 8px;
}

.semi-circle-progress path {
  transition: stroke-dasharray 0.6s ease;
}

.semi-circle-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.percentage-text {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-top: 2px;
  line-height: 1;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2px;
}

.data-card:hover::before {
  opacity: 1;
}

.data-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-8px) scale(1.02);
}

.data-overview {
  margin-right: 20px;
  position: relative;
}

.message {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.message-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.message-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

/* 最新资讯样式 */
.hight {
  height: 400px;
}
.detail-item {
  margin-top: 16px;
  padding: 16px 20px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.detail-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.detail-item:hover::before {
  opacity: 1;
}

.detail-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px) translateX(4px);
}
.detail-title {
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.detail-title-left {
  cursor: pointer;
  color: #333;
  transition: color 0.3s ease;
}
.detail-title-left:hover {
  color: #5470c6;
}
.detail-title-right {
  width: 80px;
  text-align: center;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  display: flex;
  justify-content: space-between;
}

/* 情感占比样式 */
.emotional_ratio {
  width: 240px;
  height: 240px;
  margin: 0 auto 20px;
}
.emotional-ratio-info {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}
.ratio-item {
  text-align: center;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}
.ratio-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
}
.ratio-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.ratio-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}
</style>